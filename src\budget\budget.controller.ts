import { Controller, Get, Post, Put, Delete, Body, Param } from '@nestjs/common';
import { BudgetService } from './budget.service';
import { Budget } from './budget.schema';

@Controller('finance/budget')
export class BudgetController {
  constructor(private readonly budgetService: BudgetService) {}

  @Post()
  create(@Body() budgetData: Budget) {
    return this.budgetService.create(budgetData);
  }

  @Get()
  findAll() {
    return this.budgetService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.budgetService.findOne(id);
  }

  @Put(':id')
  update(@Param('id') id: string, @Body() budgetData: Partial<Budget>) {
    return this.budgetService.update(id, budgetData);
  }

  @Delete(':id')
  delete(@Param('id') id: string) {
    return this.budgetService.delete(id);
  }
}
