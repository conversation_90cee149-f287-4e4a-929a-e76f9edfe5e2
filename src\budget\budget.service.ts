import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Budget, BudgetDocument } from './budget.schema';

@Injectable()
export class BudgetService {
  constructor(@InjectModel(Budget.name) private budgetModel: Model<BudgetDocument>) {}

  async create(data: Budget): Promise<Budget> {
    const newBudget = new this.budgetModel(data);
    return newBudget.save();
  }

  async findAll(): Promise<Budget[]> {
    return this.budgetModel.find().exec();
  }

  async findOne(id: string): Promise<Budget> {
    return this.budgetModel.findById(id).exec();
  }

  async update(id: string, data: Partial<Budget>): Promise<Budget> {
    return this.budgetModel.findByIdAndUpdate(id, data, { new: true }).exec();
  }

  async delete(id: string): Promise<any> {
    return this.budgetModel.findByIdAndDelete(id).exec();
  }
}
